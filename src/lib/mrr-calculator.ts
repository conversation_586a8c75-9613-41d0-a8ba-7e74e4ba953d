import { createHash } from 'crypto';
import Stripe from 'stripe';
import { getSubscriptionMetadata } from './stripe';

export type MRREventType = 'start' | 'upgrade' | 'downgrade' | 'cancel';

export interface MRREvent {
  uniqueSourceKey: string;
  subscriptionId: string;
  srcEventType: MRREventType;
  mrrDeltaCents: number;
  effectiveDate: string; // YYYY-MM-DD
  stripeEventId?: string;
  customerId: string;
  productId?: string;
  priceId?: string;
}

/**
 * Generate a unique source key for MRR events
 */
export function generateUniqueSourceKey(
  subscriptionId: string,
  eventType: MRREventType,
  effectiveDate: string,
  additionalData?: string
): string {
  const data = `${subscriptionId}:${eventType}:${effectiveDate}${additionalData ? `:${additionalData}` : ''}`;
  return createHash('sha256').update(data).digest('hex');
}

/**
 * Convert timestamp to YYYY-MM-DD format
 */
export function formatEffectiveDate(timestamp: number): string {
  return new Date(timestamp * 1000).toISOString().split('T')[0];
}

/**
 * Calculate MRR amount from invoice line items
 */
function calculateInvoiceMRR(invoice: Stripe.Invoice): number {
  let totalMRR = 0;

  for (const line of invoice.lines.data) {
    // Only process subscription line items
    // if (!line.subscription) continue;

    // Skip if no amount
    if (!line.amount) continue;

    // For subscription lines, we need to calculate the monthly equivalent
    // The amount is already the total for the billing period
    let monthlyAmount = line.amount;

    // If we have period information, we can be more precise
    if (line.period && line.period.start && line.period.end) {
      const periodDays = (line.period.end - line.period.start) / (24 * 60 * 60);
      const monthlyDays = 30; // Average days per month
      monthlyAmount = (line.amount / periodDays) * monthlyDays;
    }

    totalMRR += monthlyAmount;
  }

  return Math.round(totalMRR); // Return in cents
}

/**
 * Calculate MRR events from subscription and its invoice history
 * This is the most accurate method as it uses actual billing data
 */
export function calculateMRREventsFromInvoices(
  subscription: Stripe.Subscription,
  invoices: Stripe.Invoice[]
): MRREvent[] {
  const mrrEvents: MRREvent[] = [];
  const metadata = getSubscriptionMetadata(subscription);

  // Filter to only paid invoices and sort by period start
  const paidInvoices = invoices
    .filter(
      invoice => invoice.status === 'paid' && invoice.lines.data.length > 0
    )
    .sort(
      (a, b) =>
        (a.lines.data[0]?.period?.start || a.created) -
        (b.lines.data[0]?.period?.start || b.created)
    );

  let previousMRR = 0;
  let isFirstInvoice = true;

  console.log('paid invoices', paidInvoices.length);

  for (const invoice of paidInvoices) {
    const currentMRR = calculateInvoiceMRR(invoice);
    const mrrDelta = currentMRR - previousMRR;

    // Use period start date if available, otherwise use invoice creation date
    const effectiveTimestamp =
      invoice.lines.data[0]?.period?.start || invoice.created;
    const effectiveDate = formatEffectiveDate(effectiveTimestamp);

    if (isFirstInvoice && currentMRR > 0) {
      // First invoice represents the start of the subscription
      mrrEvents.push({
        uniqueSourceKey: generateUniqueSourceKey(
          subscription.id,
          'start',
          effectiveDate,
          invoice.id
        ),
        subscriptionId: subscription.id,
        srcEventType: 'start',
        mrrDeltaCents: currentMRR,
        effectiveDate,
        stripeEventId: invoice.id,
        ...metadata,
      });
      isFirstInvoice = false;
    } else if (mrrDelta !== 0) {
      // Subsequent invoices with MRR changes represent upgrades/downgrades
      const eventType: MRREventType = mrrDelta > 0 ? 'upgrade' : 'downgrade';

      mrrEvents.push({
        uniqueSourceKey: generateUniqueSourceKey(
          subscription.id,
          eventType,
          effectiveDate,
          invoice.id
        ),
        subscriptionId: subscription.id,
        srcEventType: eventType,
        mrrDeltaCents: mrrDelta,
        effectiveDate,
        stripeEventId: invoice.id,
        ...metadata,
      });
    }

    previousMRR = currentMRR;
  }

  // Add cancellation event if subscription is canceled
  if (subscription.status === 'canceled' && subscription.canceled_at) {
    const cancelDate = formatEffectiveDate(subscription.canceled_at);

    mrrEvents.push({
      uniqueSourceKey: generateUniqueSourceKey(
        subscription.id,
        'cancel',
        cancelDate
      ),
      subscriptionId: subscription.id,
      srcEventType: 'cancel',
      mrrDeltaCents: -previousMRR,
      effectiveDate: cancelDate,
      ...metadata,
    });
  }

  return mrrEvents;
}
