import { db } from '@/db/client';
import { mrrLedgerCore } from '@/db/schema';
import <PERSON><PERSON> from 'stripe';
import { calculateMRREventsFromInvoices, MRREvent } from './mrr-calculator';

export interface ProcessSubscriptionOptions {
  forceRecalculate?: boolean;
}

export interface ProcessSubscriptionResult {
  subscriptionId: string;
  eventsProcessed: number;
  eventsCreated: number;
  eventsUpdated: number;
  errors: string[];
}

/**
 * Process a single subscription and update the MRR ledger
 * This function is idempotent and can be safely re-run
 */
export async function processSubscription(
  stripe: Stripe,
  subscription: Stripe.Subscription
): Promise<void> {
  const invoices = await fetchSubscriptionInvoices(stripe, subscription.id);

  console.log(
    `Fetched ${invoices.length} invoices for subscription ${subscription.id}`
  );

  const mrrEvents = calculateMRREventsFromInvoices(subscription, invoices);

  console.log(
    `Calculated ${mrrEvents.length} MRR events for subscription ${subscription.id}`
  );

  await batchUpsertMRREvents(mrrEvents);
}

const fetchSubscriptionInvoices = async (
  stripe: Stripe,
  subscriptionId: string
) => {
  const invoices: Stripe.Invoice[] = [];
  let hasMore = true;
  let startingAfter: string | undefined;

  while (hasMore) {
    const params: Stripe.InvoiceListParams = {
      subscription: subscriptionId,
      limit: 100,
      expand: ['data.lines.data.price'],
    };

    if (startingAfter) {
      params.starting_after = startingAfter;
    }

    const invoiceList = await stripe.invoices.list(params);
    invoices.push(...invoiceList.data);

    hasMore = invoiceList.has_more;
    if (hasMore && invoiceList.data.length > 0) {
      startingAfter = invoiceList.data[invoiceList.data.length - 1].id;
    }
  }

  // Sort invoices by creation date (oldest first)
  invoices.sort((a, b) => a.created - b.created);

  return invoices;
};

/**
 * Batch upsert multiple MRR events using ON CONFLICT
 */
export async function batchUpsertMRREvents(
  mrrEvents: MRREvent[]
): Promise<void> {
  if (mrrEvents.length === 0) return;

  try {
    const eventDataArray = mrrEvents.map(mrrEvent => ({
      uniqueSourceKey: mrrEvent.uniqueSourceKey,
      subscriptionId: mrrEvent.subscriptionId,
      srcEventType: mrrEvent.srcEventType,
      mrrDeltaMinor: mrrEvent.mrrDeltaCents,
      effectiveDate: mrrEvent.effectiveDate,
      customerId: mrrEvent.customerId,
      productId: mrrEvent.productId || null,
      priceId: mrrEvent.priceId || null,
    }));

    // Use batch insert with ON CONFLICT
    await db
      .insert(mrrLedgerCore)
      .values(eventDataArray)
      .onConflictDoUpdate({
        target: mrrLedgerCore.uniqueSourceKey,
        set: {
          subscriptionId: mrrLedgerCore.subscriptionId,
          srcEventType: mrrLedgerCore.srcEventType,
          mrrDeltaMinor: mrrLedgerCore.mrrDeltaMinor,
          effectiveDate: mrrLedgerCore.effectiveDate,
          customerId: mrrLedgerCore.customerId,
          productId: mrrLedgerCore.productId,
          priceId: mrrLedgerCore.priceId,
          updatedAt: new Date(),
        },
      });

    console.log(`Batch upserted ${mrrEvents.length} MRR events`);
  } catch (error) {
    console.error('Failed to batch upsert MRR events:', error);
    throw error;
  }
}
