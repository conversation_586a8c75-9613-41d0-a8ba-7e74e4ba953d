import { processSubscription } from '@/lib/mrr-processor';
import <PERSON><PERSON> from 'stripe';
import { inngest } from '../../inngest/client';

export interface MRRSyncPayload {
  orgId: string;
}

export const mrrSyncClockedData = inngest.createFunction(
  {
    id: 'mrr-sync-test',
    name: 'Sync Test leger',
    concurrency: {
      limit: 5, // Limit concurrent executions to avoid overwhelming Stripe API
    },
  },
  { event: 'mrr/sync-test' },
  async ({ event, step, logger }) => {
    const payload = event.data as MRRSyncPayload;
    const { orgId } = payload;

    logger.info('Starting MRR sync', {
      orgId,
    });

    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-08-27.basil',
    });

    // Fetch all test clocks with pagination
    let allTestClocks: Stripe.TestHelpers.TestClock[] = [];
    let hasMoreClocks = true;
    let startingAfterClock: string | undefined;

    logger.info('Fetching all test clocks...');

    while (hasMoreClocks) {
      const testClocksResponse = await stripe.testHelpers.testClocks.list({
        limit: 100,
        starting_after: startingAfterClock,
      });

      allTestClocks.push(...testClocksResponse.data);
      hasMoreClocks = testClocksResponse.has_more;

      if (hasMoreClocks && testClocksResponse.data.length > 0) {
        startingAfterClock =
          testClocksResponse.data[testClocksResponse.data.length - 1].id;
      }
    }

    logger.info(`Found ${allTestClocks.length} test clocks to process`);

    // Process each test clock in batches using Inngest steps
    let clockIndex = 0;
    for (const testClock of allTestClocks) {
      await step.run(`process-clock-${clockIndex}`, async () => {
        logger.info(
          `Processing test clock ${clockIndex + 1}/${allTestClocks.length}`,
          {
            testClockId: testClock.id,
            testClockName: testClock.name,
          }
        );

        // Fetch all subscriptions for this test clock
        let allSubscriptions: Stripe.Subscription[] = [];
        let hasMoreSubscriptions = true;
        let startingAfterSubscription: string | undefined;

        while (hasMoreSubscriptions) {
          const subscriptionsResponse = await stripe.subscriptions.list({
            test_clock: testClock.id,
            limit: 100,
            expand: ['data.items.data.price'],
            starting_after: startingAfterSubscription,
          });

          allSubscriptions.push(...subscriptionsResponse.data);
          hasMoreSubscriptions = subscriptionsResponse.has_more;

          if (hasMoreSubscriptions && subscriptionsResponse.data.length > 0) {
            startingAfterSubscription =
              subscriptionsResponse.data[subscriptionsResponse.data.length - 1]
                .id;
          }
        }

        logger.info(
          `Found ${allSubscriptions.length} subscriptions for test clock ${testClock.id}`
        );

        if (allSubscriptions.length === 0) {
          return { processedSubscriptions: 0 };
        }

        // Process each subscription one by one using processSubscription
        let processedCount = 0;
        for (const subscription of allSubscriptions) {
          try {
            await processSubscription(stripe, subscription);
            processedCount++;
            logger.info(
              `Processed subscription ${processedCount}/${allSubscriptions.length} for test clock ${testClock.id}`,
              { subscriptionId: subscription.id }
            );
          } catch (error) {
            logger.error(
              `Failed to process subscription ${subscription.id} for test clock ${testClock.id}`,
              { error: error instanceof Error ? error.message : String(error) }
            );
          }
        }

        logger.info(
          `Completed processing ${processedCount}/${allSubscriptions.length} subscriptions for test clock ${testClock.id}`
        );

        return {
          processedSubscriptions: processedCount,
        };
      });

      clockIndex++;
    }

    logger.info(`Completed processing ${allTestClocks.length} test clocks`);
  }
);
