CREATE TYPE "public"."mrr_event_type" AS ENUM('start', 'upgrade', 'downgrade', 'cancel');--> statement-breakpoint
CREATE TABLE "mrr_ledger_core" (
	"unique_source_key" text PRIMARY KEY NOT NULL,
	"subscription_id" text NOT NULL,
	"src_event_type" "mrr_event_type" NOT NULL,
	"mrr_delta_minor" bigint NOT NULL,
	"effective_date" date NOT NULL,
	"customer_id" text,
	"product_id" text,
	"price_id" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE INDEX "mrr_ledger_subscription_idx" ON "mrr_ledger_core" USING btree ("subscription_id");--> statement-breakpoint
CREATE INDEX "mrr_ledger_effective_date_idx" ON "mrr_ledger_core" USING btree ("effective_date");--> statement-breakpoint
CREATE INDEX "mrr_ledger_customer_idx" ON "mrr_ledger_core" USING btree ("customer_id");