{"id": "790f7ca3-1344-452c-bb67-42067034e9e7", "prevId": "d77203e4-ca42-4d4a-b288-6338bf4b42f1", "version": "7", "dialect": "postgresql", "tables": {"public.mrr_ledger_core": {"name": "mrr_ledger_core", "schema": "", "columns": {"unique_source_key": {"name": "unique_source_key", "type": "text", "primaryKey": true, "notNull": true}, "subscription_id": {"name": "subscription_id", "type": "text", "primaryKey": false, "notNull": true}, "src_event_type": {"name": "src_event_type", "type": "mrr_event_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "mrr_delta_minor": {"name": "mrr_delta_minor", "type": "bigint", "primaryKey": false, "notNull": true}, "effective_date": {"name": "effective_date", "type": "date", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": false}, "price_id": {"name": "price_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"mrr_ledger_subscription_idx": {"name": "mrr_ledger_subscription_idx", "columns": [{"expression": "subscription_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "mrr_ledger_effective_date_idx": {"name": "mrr_ledger_effective_date_idx", "columns": [{"expression": "effective_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "mrr_ledger_customer_idx": {"name": "mrr_ledger_customer_idx", "columns": [{"expression": "customer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.org_integrations": {"name": "org_integrations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "org_id": {"name": "org_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "integration_provider", "typeSchema": "public", "primaryKey": false, "notNull": true}, "environment": {"name": "environment", "type": "environment", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'production'"}, "external_id": {"name": "external_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "secret_ref": {"name": "secret_ref", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "integration_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "connected_at": {"name": "connected_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "revoked_at": {"name": "revoked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"org_integrations_org_provider_env_uniq": {"name": "org_integrations_org_provider_env_uniq", "columns": [{"expression": "org_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "environment", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.environment": {"name": "environment", "schema": "public", "values": ["development", "production"]}, "public.integration_provider": {"name": "integration_provider", "schema": "public", "values": ["stripe"]}, "public.integration_status": {"name": "integration_status", "schema": "public", "values": ["pending", "connected", "error", "revoked"]}, "public.mrr_event_type": {"name": "mrr_event_type", "schema": "public", "values": ["start", "upgrade", "downgrade", "cancel"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}